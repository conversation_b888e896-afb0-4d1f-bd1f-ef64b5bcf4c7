import { db } from "@/lib/firebase"
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
  query,
  where,
  serverTimestamp,
  setDoc,
  deleteDoc,
  Timestamp,
} from "firebase/firestore"
import { BaseService } from "../base/base.service"
import { ServiceResponse } from "../base/base.types"
import {
  Invitation,
  InvitationCreateData,
  InvitationStatus,
  InvitationUpdateData,
  InvitationLink,
  InvitationLinkCreateData,
  InvitationLinkUpdateData,
  InvitationSend,
  InvitationSendCreateData,
  InvitationSendUpdateData,
} from "./invitation.types"
import { SquadService } from "../squad/squad.service"

/**
 * Invitation service for Firebase operations
 */
export class InvitationService {
  private static readonly COLLECTION = "invitations"

  /**
   * Create a new invitation
   * @param invitationData Invitation data
   * @returns The new invitation ID
   */
  static async createInvitation(invitationData: InvitationCreateData): Promise<string> {
    try {
      const invitationRef = doc(collection(db, this.COLLECTION))
      const invitationId = invitationRef.id

      await setDoc(invitationRef, {
        ...invitationData,
        id: invitationId,
        status: invitationData.status || "pending",
        createdAt: serverTimestamp(),
      })

      return invitationId
    } catch (error) {
      console.error("Error creating invitation:", error)
      throw error
    }
  }

  /**
   * Get an invitation by ID
   * @param invitationId Invitation ID
   * @returns The invitation data or null if not found
   */
  static async getInvitation(invitationId: string): Promise<Invitation | null> {
    try {
      const invitationDoc = await getDoc(doc(db, this.COLLECTION, invitationId))

      if (invitationDoc.exists()) {
        return { ...invitationDoc.data(), id: invitationId } as Invitation
      }

      return null
    } catch (error) {
      console.error("Error getting invitation:", error)
      throw error
    }
  }

  /**
   * Get invitations for a squad
   * @param squadId Squad ID
   * @returns Array of invitations
   */
  static async getSquadInvitations(squadId: string): Promise<Invitation[]> {
    try {
      const q = query(collection(db, this.COLLECTION), where("squadId", "==", squadId))
      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as Invitation)
    } catch (error) {
      console.error("Error getting squad invitations:", error)
      throw error
    }
  }

  /**
   * Get invitations for a user (by invitee email)
   * @param email User email
   * @returns Array of invitations
   */
  static async getUserInvitations(email: string): Promise<Invitation[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION),
        where("inviteeEmail", "==", email),
        where("status", "==", "pending")
      )

      const querySnapshot = await getDocs(q)

      return querySnapshot.docs.map((doc) => ({ ...doc.data(), id: doc.id }) as Invitation)
    } catch (error) {
      console.error("Error getting user invitations:", error)
      throw error
    }
  }

  /**
   * Update an invitation
   * @param invitationId Invitation ID
   * @param invitationData Invitation data to update
   * @returns Service response
   */
  static async updateInvitation(
    invitationId: string,
    invitationData: InvitationUpdateData
  ): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, invitationId), {
        ...invitationData,
        lastUpdated: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating invitation:", error)
      return { success: false, error }
    }
  }

  /**
   * Update invitation status
   * @param invitationId Invitation ID
   * @param status New status
   * @param userId User ID (for accepted invitations)
   * @returns Service response
   */
  static async updateInvitationStatus(
    invitationId: string,
    status: InvitationStatus,
    userId?: string
  ): Promise<ServiceResponse> {
    try {
      const invitation = await this.getInvitation(invitationId)

      if (!invitation) {
        return { success: false, error: new Error("Invitation not found") }
      }

      // Update invitation status
      await updateDoc(doc(db, this.COLLECTION, invitationId), {
        status,
        lastUpdated: serverTimestamp(),
      })

      // If accepting invitation, add user to squad
      if (status === "accepted" && userId) {
        await SquadService.addMember(invitation.squadId, userId)

        // Create invitation send record for tracking (legacy invitation compatibility)
        if (invitation.inviteeEmail) {
          try {
            const { InvitationSendService } = await import("./invitation-send.service")
            await InvitationSendService.createInvitationSend(invitation.squadId, {
              invitationId,
              email: invitation.inviteeEmail.toLowerCase(),
            })

            // Update the send status to joined
            const sends = await InvitationSendService.getInvitationSends(
              invitation.squadId,
              invitationId
            )
            const userSend = sends.find((send) => send.email === invitation.inviteeEmail.toLowerCase())
            if (userSend) {
              await InvitationSendService.updateInvitationSendStatus(
                invitation.squadId,
                userSend.id,
                "joined"
              )
            }
          } catch (err) {
            // Don't fail the invitation acceptance if tracking fails
            console.warn("Failed to track invitation send for legacy invitation:", err)
          }
        }
      }

      return { success: true }
    } catch (error) {
      console.error("Error updating invitation status:", error)
      return { success: false, error }
    }
  }

  /**
   * Delete an invitation
   * @param invitationId Invitation ID
   * @returns Service response
   */
  static async deleteInvitation(invitationId: string): Promise<ServiceResponse> {
    try {
      await deleteDoc(doc(db, this.COLLECTION, invitationId))
      return { success: true }
    } catch (error) {
      console.error("Error deleting invitation:", error)
      return { success: false, error }
    }
  }

  /**
   * Resend an invitation
   * @param invitationId Invitation ID
   * @returns Service response
   */
  static async resendInvitation(invitationId: string): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.COLLECTION, invitationId), {
        lastResent: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error resending invitation:", error)
      return { success: false, error }
    }
  }

  // ===== INVITATION LINK METHODS =====

  private static readonly INVITATION_LINKS_COLLECTION = "invitations"

  /**
   * Create a new invitation link
   * @param invitationData Invitation link data
   * @returns The new invitation link ID
   */
  static async createInvitationLink(invitationData: InvitationLinkCreateData): Promise<string> {
    try {
      const invitationRef = doc(collection(db, this.INVITATION_LINKS_COLLECTION))
      const invitationId = invitationRef.id

      // Set expiration to 10 days from now
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 10)

      await setDoc(invitationRef, {
        ...invitationData,
        id: invitationId,
        expiresAt: Timestamp.fromDate(expiresAt),
        isActive: true,
        createdAt: serverTimestamp(),
      })

      return invitationId
    } catch (error) {
      console.error("Error creating invitation link:", error)
      throw error
    }
  }

  /**
   * Get an invitation link by ID
   * @param invitationId Invitation link ID
   * @returns The invitation link or null if not found
   */
  static async getInvitationLink(invitationId: string): Promise<InvitationLink | null> {
    try {
      const invitationDoc = await getDoc(doc(db, this.INVITATION_LINKS_COLLECTION, invitationId))
      if (invitationDoc.exists()) {
        return { ...invitationDoc.data(), id: invitationId } as InvitationLink
      }
      return null
    } catch (error) {
      console.error("Error getting invitation link:", error)
      throw error
    }
  }

  /**
   * Get active invitation link for a squad
   * @param squadId Squad ID
   * @returns The active invitation link or null if not found
   */
  static async getActiveInvitationLink(squadId: string): Promise<InvitationLink | null> {
    try {
      const invitationsRef = collection(db, this.INVITATION_LINKS_COLLECTION)
      const q = query(
        invitationsRef,
        where("squadId", "==", squadId),
        where("isActive", "==", true)
      )
      const querySnapshot = await getDocs(q)

      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0]
        const invitationLink = { ...doc.data(), id: doc.id } as InvitationLink

        // Check if the invitation is expired
        const now = new Date()
        const expiresAt = invitationLink.expiresAt.toDate()

        if (expiresAt > now) {
          return invitationLink
        } else {
          // Deactivate expired invitation
          await this.updateInvitationLink(invitationLink.id, { isActive: false })
          return null
        }
      }

      return null
    } catch (error) {
      console.error("Error getting active invitation link:", error)
      throw error
    }
  }

  /**
   * Update an invitation link
   * @param invitationId Invitation link ID
   * @param invitationData Invitation link data to update
   * @returns Service response
   */
  static async updateInvitationLink(
    invitationId: string,
    invitationData: InvitationLinkUpdateData
  ): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.INVITATION_LINKS_COLLECTION, invitationId), {
        ...invitationData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating invitation link:", error)
      return { success: false, error }
    }
  }

  /**
   * Deactivate an invitation link
   * @param invitationId Invitation link ID
   * @returns Service response
   */
  static async deactivateInvitationLink(invitationId: string): Promise<ServiceResponse> {
    try {
      await updateDoc(doc(db, this.INVITATION_LINKS_COLLECTION, invitationId), {
        isActive: false,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error deactivating invitation link:", error)
      return { success: false, error }
    }
  }
}
